import { defineStore } from 'pinia';
import axios from 'axios';
import router from '@/router';
import Cookies from 'js-cookie';

// Add TypeScript declaration for window.i18n
declare global {
    interface Window {
        i18n?: {
            global: {
                locale: {
                    value: string
                }
            }
        };
    }
}

interface User {
    id: string;
    full_name: string;
    email: string;
    [key: string]: any;
}

interface AuthState {
    user: User | null;
    token: string | null;
    permissions: string[];
    permissionsVersion: number;
    roles: string[];
    isAuthenticated: boolean;
    loading: boolean;
    language: string;
}

export const useAuthStore = defineStore('auth', {
state: (): AuthState => ({
    user: null,
    token: null,
    permissions: [],
    permissionsVersion: 0,
    roles: [],
    isAuthenticated: false,
    loading: false,
    language: localStorage.getItem('app_language') || 'vi'
}),

getters: {
	/**
	 * Check if user has a specific permission
	 */
    hasPermission: (state) => (permission: string): boolean => {
        return state.permissions.includes(permission);
    },

	/**
	 * Check if user has at least one of the permissions
	 */
    hasAnyPermission: (state) => (permissions: string[]): boolean => {
        return permissions.some(permission => state.permissions.includes(permission));
    },

	/**
	 * Check if user has all permissions
	 */
    hasAllPermissions: (state) => (permissions: string[]): boolean => {
        return permissions.every(permission => state.permissions.includes(permission));
    },

	/**
	 * Check if user has a specific role
	 */
    hasRole: (state) => (role: string): boolean => {
        return state.roles.includes(role);
    }
},

actions: {
	/**
	 * Handle login
	 */
    async login(accountName: string, password: string, rememberMe: boolean = false) {
        this.loading = true;
        
        try {
            const response = await axios.post('/api/auth/login', {
                account_name: accountName,
                password,
                remember_me: rememberMe
            });

            const { user, permissions = [], roles = [], permissions_version = 0, access_token, language } = response.data;
            const cookie_data = response.data.cookie_data || { expires_days: 1 };
            
            // Verify all required data is present
            if (!user || !access_token) {
                return { success: false, message: 'Invalid server response: missing user data or token' };
            }
            
            this.setAuthData(user, permissions, roles, permissions_version, access_token, language);
            
            // Set cookie with proper error handling
            try {
                const expiresInDays = cookie_data.expires_days || 1;
                Cookies.set('__Host_tvnas_token', access_token, { 
                    expires: expiresInDays, 
                    secure: window.location.protocol === 'https:',
                    sameSite: 'strict'
                });
            } catch (cookieError) {
                console.error('Error setting cookie:', cookieError);
                // Continue anyway, as we still have the token in memory
            }
            
            // Set the default authorization header for all future requests
            axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
            
            // Redirect to the home or saved route
            const redirectPath = Cookies.get('redirect_path') || '/home';
            Cookies.remove('redirect_path');
            router.push(redirectPath);
            
            return true;
        } catch (error: any) {
            console.error('Login error:', error);
            
            // Extract error message from response if available
            let errorMessage = 'An error occurred during login';
            
            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                if (error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                }
                console.log('Error status:', error.response.status);
                console.log('Error data:', error.response.data);
            } else if (error.request) {
                // The request was made but no response was received
                errorMessage = 'No response from server. Please check your internet connection.';
                console.log('Error request:', error.request);
            }
            
            // Return the error message for display in the UI
            return { success: false, message: errorMessage };
        } finally {
            this.loading = false;
        }
    },

	/**
	 * Logout user
	 */
    async logout() {
        try {
            if (this.isAuthenticated) {
                await axios.get('/api/auth/logout');
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.clearAuthData();
            router.push('/login');
        }
    },

	/**
	 * Set authentication data in store
	 */
    setAuthData(user: User, permissions: string[], roles: string[], permissionsVersion: number, token: string, language?: string) {
        this.user = user;
        this.permissions = permissions;
        this.roles = roles;
        this.permissionsVersion = permissionsVersion;
        this.token = token;
        this.isAuthenticated = true;
        
        // Check if user has already set a language preference in localStorage
        const userPreferredLanguage = localStorage.getItem('app_language');
        
        // If user hasn't set language preference, use the one from server
        // Otherwise, respect user's choice but update the user object
        if (!userPreferredLanguage && language) {
            this.language = language;
            localStorage.setItem('app_language', language);
            
            // Update app locale if i18n is available
            if (window.i18n && typeof window.i18n.global.locale !== 'undefined') {
                window.i18n.global.locale.value = language;
            }
        } else if (userPreferredLanguage) {
            this.language = userPreferredLanguage;
            
            // Update app locale if i18n is available
            if (window.i18n && typeof window.i18n.global.locale !== 'undefined') {
                window.i18n.global.locale.value = userPreferredLanguage;
            }
        }
    },

	/**
	 * Clear authentication data
	 */
    clearAuthData() {
        this.user = null;
        this.token = null;
        this.permissions = [];
        this.permissionsVersion = 0;
        this.roles = [];
        this.isAuthenticated = false;
        
        // We don't clear language on logout to maintain user preference
        
        // Remove token from cookies and axios headers
        Cookies.remove('__Host_tvnas_token');
        delete axios.defaults.headers.common['Authorization'];
    },

	/**
	 * Fetch current user and permissions
	 */
    async fetchCurrentUser() {
        console.log(33);
        
        this.loading = true;
        
        try {
            const response = await axios.get('/api/auth/user');
            const { user, permissions, roles, permissions_version, language } = response.data;
            
            this.user = user;
            this.permissions = permissions;
            this.roles = roles;
            this.permissionsVersion = permissions_version;
            this.isAuthenticated = true;
            
            // Check if user has already set a language preference in localStorage
            const userPreferredLanguage = localStorage.getItem('app_language');
            
            // If user hasn't set language preference, use the one from server
            // Otherwise, respect user's choice
            if (!userPreferredLanguage && language) {
                this.language = language;
                localStorage.setItem('app_language', language);
                
                // Update app locale if i18n is available
                if (window.i18n && typeof window.i18n.global.locale !== 'undefined') {
                    window.i18n.global.locale.value = language;
                }
            } else if (userPreferredLanguage) {
                // Respect user's choice
                this.language = userPreferredLanguage;
                
                // Update app locale if i18n is available
                if (window.i18n && typeof window.i18n.global.locale !== 'undefined') {
                    window.i18n.global.locale.value = userPreferredLanguage;
                }
            }
            
            return true;
        } catch (error) {
            console.error('Error fetching user:', error);
            this.clearAuthData();
            return false;
        } finally {
            this.loading = false;
        }
    },

    /**
     * Check if permissions have been updated
     */
    async checkPermissionsUpdate() {
        try {
            const response = await axios.get('/api/auth/check-permissions');
            const serverVersion = response.data.permissions_version;
            
            // If server version is newer than our stored version, fetch updated permissions
            if (serverVersion > this.permissionsVersion) {
                await this.fetchCurrentUser();
            }
            
            return true;
        } catch (error) {
            console.error('Error checking permissions:', error);
            return false;
        }
    },

    /**
     * Refresh the token and user data
     */
    async refreshToken() {
        try {
            const response = await axios.get('/api/auth/refresh');
            const { user, permissions = [], roles = [], permissions_version = 0, access_token, language } = response.data;
            const cookie_data = response.data.cookie_data || { expires_days: 1 };
            
            if (!user || !access_token) {
                console.error('Invalid response when refreshing token: missing user or token data');
                return false;
            }
            
            this.setAuthData(user, permissions, roles, permissions_version, access_token, language);
            
            // Update cookie and axios headers
            try {
                const expiresInDays = cookie_data.expires_days || 1;
                Cookies.set('__Host_tvnas_token', access_token, { 
                    expires: expiresInDays, 
                    secure: window.location.protocol === 'https:',
                    sameSite: 'strict'
                });
            } catch (cookieError) {
                console.error('Error setting cookie during token refresh:', cookieError);
                // Continue anyway, as we still have the token in memory
            }
            
            axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
            
            return true;
        } catch (error: any) {
            console.error('Error refreshing token:', error);
            
            // Log additional error details if available
            if (error.response) {
                console.log('Error status:', error.response.status);
                console.log('Error data:', error.response.data);
            }
            
            this.clearAuthData();
            return false;
        }
    },

    /**
     * Restore auth state from cookies on app initialization
     */
    initAuth() {
        const token = Cookies.get('__Host_tvnas_token');
        
        if (token) {
            // Set the default authorization header
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            this.token = token;
            
            // Fetch current user data to verify the token is still valid
            return this.fetchCurrentUser();
        }
        
        return Promise.resolve(false);
    }
}
});
